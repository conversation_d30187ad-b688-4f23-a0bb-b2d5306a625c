import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_in_app_messaging/firebase_in_app_messaging.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:promobell/flavors/firebase/firebase_options.dart'
    show FirebaseFlavorOptions;
import 'package:promobell/src/models/product.dart';
import 'package:promobell/src/services/logs/app_logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:timezone/data/latest.dart' as tz;

import '../firebase_messaging_service.dart';
import '../src/app/app_module.dart';
import '../src/app/app_widget.dart';
import '../src/services/sqflite.dart/init_sqflite.dart';
import '../supa_base_config_keys.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

final FlutterLocalNotificationsPlugin
flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

Future<Map<String, dynamic>> _loadEnvFile(String flavor) async {
  final envFileName =
      flavor == 'prod' ? '.env' : '.envDesenvolvimento';
  final content = await rootBundle.loadString(envFileName);
  return jsonDecode(content);
}

Future<void> firebaseBackgroundMessageHandler(
  RemoteMessage message,
) async {
  try {
    final data = message.data;
    final prefs = await SharedPreferences.getInstance();
    final rawName = prefs.getString('userName');
    final userName =
        (rawName != null && rawName.trim().isNotEmpty)
            ? rawName
            : 'Promolover';

    final personalizedTitle = data['title']?.replaceAll(
      '{name}',
      userName,
    );
    final personalizedBody = data['body']?.replaceAll(
      '{name}',
      userName,
    );

    const androidDetails = AndroidNotificationDetails(
      'lembretes_notifications_status_compras',
      'status_compras',
      channelDescription: 'Notificacoes de status de compras',
      importance: Importance.max,
      priority: Priority.max,
      icon: '@drawable/notification',
      color: Color(0xFF4141E1),
    );

    const iosDetails = DarwinNotificationDetails(
      threadIdentifier: 'thread_id',
      presentAlert: true,
      presentBadge: true,
      presentSound: false,
      presentBanner: true,
    );

    final platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      personalizedTitle ?? data['title'],
      personalizedBody ?? data['body'],
      platformDetails,
      payload: data['route'] ?? '',
    );

    if (kDebugMode) {
      print('Background notification showed');
      print("data: $data");
      print("personalizedTitle: $personalizedTitle");
      print("personalizedBody: $personalizedBody");
    }
  } catch (e, stack) {
    if (kDebugMode) {
      print('Erro no background handler: $e\n$stack');
    }
  }
}

Future<Product> getProduct(String id) async {
  final int idProduto = int.parse(id);
  final supabase = Supabase.instance.client;

  try {
    final data =
        await supabase
            .from('produtos_cadastro')
            .select()
            .eq('id', idProduto)
            .select();

    if (data.isEmpty) {
      throw Exception('Produto não encontrado');
    }

    final Product produto = Product.fromMap(data.first);
    return produto;
  } catch (e, stackTrace) {
    AppLogger.logError('Erro ao buscar produto', e, stackTrace);
    rethrow;
  }
}

Future<void> bootstrap(String flavor) async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    final env = await _loadEnvFile(flavor);

    final supaBaseConfigs = SupaBaseConfigKeys(
      apiUrl: env['API_URL'],
      apiAnonKey: env['API_ANON_KEY'],
      apiServiceRoles: env['API_SERVICE_ROLES'],
      googleWebClientId: env['GOOGLE_WEB_CLIENT_ID'],
      androidClientId: env['ANDROID_CLIENT_ID'],
      iosClientId: env['IOS_CLIENT_ID'],
      gcpProjectId: env['GCP_PROJECT_ID'],
    );

    tz.initializeTimeZones();
    FlutterNativeSplash.remove();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    FirebaseOptions firebaseOptions;
    switch (flavor) {
      case 'prod':
        firebaseOptions = FirebaseFlavorOptions.prod;
        break;
      case 'dev':
      default:
        firebaseOptions = FirebaseFlavorOptions.dev;
        break;
    }

    await Firebase.initializeApp(options: firebaseOptions);
    FirebaseMessaging.onBackgroundMessage(
      firebaseBackgroundMessageHandler,
    );

    await Supabase.initialize(
      url: supaBaseConfigs.apiUrl,
      anonKey: supaBaseConfigs.apiAnonKey,
      realtimeClientOptions: const RealtimeClientOptions(
        eventsPerSecond: 2,
      ),
    );

    await InitSqflite().init();

    runApp(ModularApp(module: AppModule(), child: const AppWidget()));

    // Aguarda o Modular inicializar para pegar o singleton
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final messagingService =
          Modular.get<FirebaseMessagingService>();
      await messagingService.initNotification();
      await messagingService.getInstallationId();
      await messagingService.handleInitialNotification();

      final fiam = FirebaseInAppMessaging.instance;
      await fiam.setMessagesSuppressed(false);
    });
  } catch (e, stack) {
    AppLogger.logError('Erro no bootstrap', e, stack);
    rethrow;
  }
}
