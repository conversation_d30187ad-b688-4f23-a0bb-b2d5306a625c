import 'dart:async';
import 'package:promobell/src/models/product.dart';
import 'package:promobell/src/services/logs/app_logger.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_app_installations/firebase_app_installations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/offers/offers_module.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'received_notificationn.dart';

class FirebaseMessagingService extends ChangeNotifier {
  static final FirebaseMessagingService _instance =
      FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  final _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin
  _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initNotification() async {
    try {
      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.requestNotificationsPermission();

      await _initLocalNotifications();
      await _setupInteractedMessage();
      await getToken();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> getInstallationId() async {
    final installations = FirebaseInstallations.instance;
    final id = await installations.getId();
    if (kDebugMode) print('Firebase Installation ID: $id');
  }

  Future<String?> getToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      if (kDebugMode) print('Firebase Token: $token');
      return token;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> _setupInteractedMessage() async {
    await _firebaseMessaging
        .setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
        );

    final initialMessage =
        await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) _handleMessage(initialMessage);

    FirebaseMessaging.onMessage.listen(_handleMessage);

    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      final route = message.data['route'];
      if (route != null && route.isNotEmpty) {
        handleNotificationRoute(route);
      } else {
        if (kDebugMode)
          print("Notificação tocada sem route no onMessageOpenedApp");
      }
    });
  }

  Future<void> handleInitialNotification() async {
    final initialMessage =
        await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      final payload = initialMessage.data['route'];
      if (payload != null) handleNotificationRoute(payload);
    }
  }

  Future<void> _handleMessage(RemoteMessage message) async {
    final data = message.data;
    final title = data['title'] ?? message.notification?.title;
    final body = data['body'] ?? message.notification?.body;

    await showNotification(
      ReceivedNotification(
        id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        title: title,
        body: body,
        payload: data['route'] ?? '',
      ),
    );
  }

  Future<void> _initLocalNotifications() async {
    const android = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    final iOS = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestCriticalPermission: true,
      defaultPresentBanner: true,
      defaultPresentBadge: true,
    );

    final settings = InitializationSettings(
      android: android,
      iOS: iOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      settings,
      onDidReceiveNotificationResponse: (
        NotificationResponse response,
      ) {
        final payload = response.payload;
        if (payload != null && payload.isNotEmpty) {
          handleNotificationRoute(payload);
        }
      },
    );
  }

  Future<void> showNotification(
    ReceivedNotification receivedNotification,
  ) async {
    const androidDetails = AndroidNotificationDetails(
      'lembretes_notifications_status_compras',
      'status_compras',
      channelDescription: 'Notificações de status de compras',
      importance: Importance.max,
      priority: Priority.max,
      color: Color(0xFF4141E1),
      enableVibration: true,
      enableLights: true,
      visibility: NotificationVisibility.public,
      icon: '@drawable/notification',
      category: AndroidNotificationCategory.alarm,
      channelShowBadge: true,
      colorized: true,
    );

    const iosDetails = DarwinNotificationDetails(
      threadIdentifier: 'thread_id',
      presentAlert: true,
      presentBadge: true,
      presentSound: false,
      presentBanner: true,
    );

    final platformDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    final prefs = await SharedPreferences.getInstance();
    final rawName = prefs.getString('userName');
    final userName =
        (rawName != null && rawName.trim().isNotEmpty)
            ? rawName
            : 'Promolover';

    final personalizedTitle = receivedNotification.title?.replaceAll(
      '{name}',
      userName,
    );
    final personalizedBody = receivedNotification.body?.replaceAll(
      '{name}',
      userName,
    );

    await _flutterLocalNotificationsPlugin.show(
      receivedNotification.id,
      personalizedTitle ?? receivedNotification.title,
      personalizedBody ?? receivedNotification.body,
      platformDetails,
      payload: receivedNotification.payload,
    );

    if (kDebugMode) print('Notification showed');
  }

  Future<void> handleNotificationRoute(String? payload) async {
    print('route payload: $payload');
    if (payload == null || payload.isEmpty) return;

    try {
      if (payload.contains('/product?id=')) {
        final id = payload.split('id=').last;
        final product = await getProduct(id);
        Modular.to.pushNamed(
          '/offers${OffersModule.productDetails}',
          arguments: product,
        );
      } else {
        Modular.to.pushNamed(payload);
      }
    } catch (e, stack) {
      AppLogger.logError(
        'Erro ao navegar pela notificação',
        e,
        stack,
      );
    }
  }

  Future<Product> getProduct(String id) async {
    final int idProduto = int.parse(id);
    final supabase = Supabase.instance.client;

    try {
      final data =
          await supabase
              .from('produtos_cadastro')
              .select()
              .eq('id', idProduto)
              .select();
      if (data.isEmpty) throw Exception('Produto não encontrado');
      return Product.fromMap(data.first);
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }
}
